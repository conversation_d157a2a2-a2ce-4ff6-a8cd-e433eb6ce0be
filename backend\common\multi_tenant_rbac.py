"""
Multi-Tenant Role-Based Access Control (RBAC) System

Handles hierarchical tenant relationships and permissions:
- City Admin manages Subcity Users
- Subcity Admin manages Kebele Users  
- Tenant-specific permissions
- Cross-tenant access control
"""

from django.contrib.auth.models import Permission
from django.db import connection
from tenants.models import Tenant


class MultiTenantRBAC:
    """Multi-tenant RBAC permission checker"""
    
    # Tenant hierarchy levels
    TENANT_LEVELS = {
        'system': 0,
        'city': 1,
        'subcity': 2,
        'kebele': 3
    }
    
    @staticmethod
    def has_permission(user, permission_codename):
        """
        Check if user has permission within their tenant context
        """
        if not user or not hasattr(user, 'tenant'):
            return False
            
        # Superuser bypass
        if user.is_superuser:
            return True
            
        # Check user permissions in current schema
        return user.user_permissions.filter(codename=permission_codename).exists() or \
               user.groups.filter(permissions__codename=permission_codename).exists()
    
    @staticmethod
    def can_manage_users_in_tenant(user, target_tenant):
        """
        Check if user can manage users in a specific tenant
        
        Rules:
        - City admin can manage subcity users (if subcity is child of city)
        - Subcity admin can manage kebele users (if kebele is child of subcity)
        - Users can only manage users in direct child tenants
        """
        if not user or not target_tenant:
            return False
            
        # Superuser can manage anywhere
        if user.is_superuser:
            return True
            
        # System admin can manage anywhere
        if MultiTenantRBAC.has_permission(user, 'manage_all_users'):
            return True
            
        user_tenant = user.tenant
        
        # City admin managing subcity users
        if (user_tenant.type == 'city' and 
            target_tenant.type == 'subcity' and
            target_tenant.parent_id == user_tenant.id):
            return MultiTenantRBAC.has_permission(user, 'create_subcity_users')
            
        # Subcity admin managing kebele users  
        if (user_tenant.type == 'subcity' and
            target_tenant.type == 'kebele' and
            target_tenant.parent_id == user_tenant.id):
            return MultiTenantRBAC.has_permission(user, 'create_kebele_users')
            
        return False
    
    @staticmethod
    def can_access_tenant_data(user, target_tenant):
        """
        Check if user can access data from a specific tenant
        
        Rules:
        - Users can access their own tenant data
        - City admin can access child subcity data
        - Subcity admin can access child kebele data
        """
        if not user or not target_tenant:
            return False
            
        # Superuser can access all data
        if user.is_superuser:
            return True
            
        # System admin can access all data
        if MultiTenantRBAC.has_permission(user, 'view_all_tenants_data'):
            return True
            
        user_tenant = user.tenant
        
        # Users can access their own tenant data
        if user_tenant.id == target_tenant.id:
            return True
            
        # City admin accessing child subcity data
        if (user_tenant.type == 'city' and 
            target_tenant.type == 'subcity' and
            target_tenant.parent_id == user_tenant.id):
            return MultiTenantRBAC.has_permission(user, 'view_child_subcities_data')
            
        # Subcity admin accessing child kebele data
        if (user_tenant.type == 'subcity' and
            target_tenant.type == 'kebele' and
            target_tenant.parent_id == user_tenant.id):
            return MultiTenantRBAC.has_permission(user, 'view_child_kebeles_data')
            
        return False
    
    @staticmethod
    def can_manage_workflow(user, target_tenant):
        """
        Check if user can manage workflow for a specific tenant

        Rules:
        - Subcity admin can manage workflows for child kebeles
        - City admin can manage workflows for child subcities
        - Superusers can manage all workflows
        """
        if not user or not target_tenant:
            return False

        # Superusers can manage all workflows
        if user.is_superuser:
            return True

        # Check if user has explicit workflow management permission
        has_workflow_perm = MultiTenantRBAC.has_permission(user, 'manage_workflows')

        # Check role-based permissions for workflow management
        user_tenant = getattr(user, 'tenant', None)
        if not user_tenant:
            return False

        # Subcity admin managing kebele workflows
        if (user.role == 'subcity_admin' and
            user_tenant.type == 'subcity' and
            target_tenant.type == 'kebele' and
            target_tenant.parent_id == user_tenant.id):
            return True

        # City admin managing subcity workflows
        if (user.role == 'city_admin' and
            user_tenant.type == 'city' and
            target_tenant.type == 'subcity' and
            target_tenant.parent_id == user_tenant.id):
            return True

        # If user has explicit workflow permission, allow based on hierarchy
        if has_workflow_perm:
            # Subcity admin with permission can manage child kebeles
            if (user_tenant.type == 'subcity' and
                target_tenant.type == 'kebele' and
                target_tenant.parent_id == user_tenant.id):
                return True

            # City admin with permission can manage child subcities
            if (user_tenant.type == 'city' and
                target_tenant.type == 'subcity' and
                target_tenant.parent_id == user_tenant.id):
                return True
            
        return False
    
    @staticmethod
    def get_manageable_tenants(user):
        """
        Get list of tenants that user can manage
        """
        if not user:
            return Tenant.objects.none()
            
        # Superuser can manage all tenants
        if user.is_superuser:
            return Tenant.objects.all()
            
        # System admin can manage all tenants
        if MultiTenantRBAC.has_permission(user, 'manage_all_tenants'):
            return Tenant.objects.all()
            
        user_tenant = user.tenant
        manageable_tenants = []
        
        # City admin can manage child subcities
        if (user_tenant.type == 'city' and 
            MultiTenantRBAC.has_permission(user, 'create_subcity_users')):
            manageable_tenants.extend(
                Tenant.objects.filter(type='subcity', parent=user_tenant)
            )
            
        # Subcity admin can manage child kebeles
        if (user_tenant.type == 'subcity' and
            MultiTenantRBAC.has_permission(user, 'create_kebele_users')):
            manageable_tenants.extend(
                Tenant.objects.filter(type='kebele', parent=user_tenant)
            )
            
        return manageable_tenants
    
    @staticmethod
    def get_accessible_tenants(user):
        """
        Get list of tenants whose data user can access
        """
        if not user:
            return Tenant.objects.none()
            
        # Superuser can access all tenants
        if user.is_superuser:
            return Tenant.objects.all()
            
        # System admin can access all tenants
        if MultiTenantRBAC.has_permission(user, 'view_all_tenants_data'):
            return Tenant.objects.all()
            
        user_tenant = user.tenant
        accessible_tenants = [user_tenant]  # Always include own tenant
        
        # City admin can access child subcities
        if (user_tenant.type == 'city' and 
            MultiTenantRBAC.has_permission(user, 'view_child_subcities_data')):
            accessible_tenants.extend(
                Tenant.objects.filter(type='subcity', parent=user_tenant)
            )
            
        # Subcity admin can access child kebeles
        if (user_tenant.type == 'subcity' and
            MultiTenantRBAC.has_permission(user, 'view_child_kebeles_data')):
            accessible_tenants.extend(
                Tenant.objects.filter(type='kebele', parent=user_tenant)
            )
            
        return accessible_tenants
    
    @staticmethod
    def apply_tenant_permissions(user, tenant_type, workflow_type='centralized'):
        """
        Apply appropriate permissions based on tenant type and workflow
        """
        if not user or not hasattr(user, 'tenant'):
            return False
            
        # Switch to user's tenant schema
        connection.set_schema(user.tenant.schema_name)
        
        # Define permission sets by tenant type and workflow
        permission_sets = {
            'kebele': {
                'centralized': [
                    'register_citizens',
                    'view_citizens_list', 
                    'view_citizen_details',
                    'generate_id_cards',
                    'view_id_cards_list',
                    'view_kebele_dashboard',
                    'view_kebele_reports'
                ],
                'autonomous': [
                    'register_citizens',
                    'view_citizens_list',
                    'view_citizen_details', 
                    'generate_id_cards',
                    'view_id_cards_list',
                    'approve_id_cards',
                    'print_idcards',
                    'local_document_verification',
                    'view_kebele_dashboard',
                    'view_kebele_reports'
                ]
            },
            'subcity': {
                'default': [
                    'view_child_kebeles_data',
                    'create_kebele_users',
                    'approve_id_cards',
                    'print_id_cards',
                    'verify_documents',
                    'view_subcity_dashboard',
                    'view_subcity_reports',
                    'manage_workflows'
                ]
            },
            'city': {
                'default': [
                    'view_child_subcities_data',
                    'create_subcity_users',
                    'view_city_dashboard',
                    'view_city_reports',
                    'manage_workflows'
                ]
            }
        }
        
        # Get appropriate permission set
        tenant_perms = permission_sets.get(tenant_type, {})
        if tenant_type == 'kebele':
            permissions = tenant_perms.get(workflow_type, tenant_perms.get('centralized', []))
        else:
            permissions = tenant_perms.get('default', [])
            
        # Apply permissions to user
        applied_count = 0
        for perm_code in permissions:
            try:
                permission = Permission.objects.get(codename=perm_code)
                user.user_permissions.add(permission)
                applied_count += 1
            except Permission.DoesNotExist:
                print(f"Permission {perm_code} not found")
                
        print(f"Applied {applied_count} permissions to {user.email} in {user.tenant.name}")
        return applied_count > 0


# Convenience functions for use in views and templates
def has_permission(user, permission):
    """Shortcut function for permission checking"""
    return MultiTenantRBAC.has_permission(user, permission)

def can_manage_users_in_tenant(user, target_tenant):
    """Shortcut function for user management checking"""
    return MultiTenantRBAC.can_manage_users_in_tenant(user, target_tenant)

def can_access_tenant_data(user, target_tenant):
    """Shortcut function for data access checking"""
    return MultiTenantRBAC.can_access_tenant_data(user, target_tenant)

def can_manage_workflow(user, target_tenant):
    """Shortcut function for workflow management checking"""
    return MultiTenantRBAC.can_manage_workflow(user, target_tenant)
