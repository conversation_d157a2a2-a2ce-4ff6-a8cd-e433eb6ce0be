/**
 * Dynamic Role Detection Based on Permissions
 * 
 * Instead of using static user.role, detect roles dynamically from permissions
 */

/**
 * Role definitions based on permission combinations
 */
export const ROLE_DEFINITIONS = {
  superadmin: {
    name: 'Super Admin',
    requiredPermissions: ['full_system_access', 'manage_all_tenants'],
    anyPermission: true // User needs ANY of these permissions
  },
  
  city_admin: {
    name: 'City Admin', 
    requiredPermissions: ['view_child_subcities_data', 'create_subcity_users'],
    anyPermission: true
  },
  
  subcity_admin: {
    name: 'Subcity Admin',
    requiredPermissions: ['view_child_kebeles_data', 'create_kebele_users'],
    anyPermission: true
  },
  
  kebele_leader: {
    name: '<PERSON><PERSON><PERSON> Leader',
    requiredPermissions: ['approve_id_cards', 'verify_documents'],
    anyPermission: true
  },
  
  clerk: {
    name: 'Clerk',
    requiredPermissions: ['register_citizens', 'generate_id_cards'],
    anyPermission: true
  },
  
  printer: {
    name: 'Printer',
    requiredPermissions: ['print_id_cards', 'print_idcards'],
    anyPermission: true
  },
  
  viewer: {
    name: 'Viewer',
    requiredPermissions: ['view_citizens_list', 'view_id_cards_list'],
    anyPermission: true
  }
};

// Dynamic role detection removed - using static roles only

// Dynamic role functions removed - using static roles only

/**
 * Get all roles a user qualifies for (user might have multiple roles)
 * @param {Object} user - User object
 * @returns {Array} - Array of role keys user qualifies for
 */
// getAllUserRoles function removed - using static roles only

// getRoleBadge function removed - using static roles only

/**
 * Navigation generation based on dynamic roles and permissions
 * @param {Object} user - User object
 * @returns {Array} - Navigation items
 */
export const generateDynamicNavigation = (user) => {
  console.log('🚀 DYNAMIC NAVIGATION: Function called!');
  console.log('🔍 Generating navigation for user:', user?.email);
  console.log('🔍 User role:', user?.role);
  console.log('🔍 User permissions:', user?.permissions);

  const navigationItems = [];

  // Special case: Users with designated_printer role should ONLY see Print Queue
  if (user?.role === 'designated_printer') {
    console.log('🖨️ SPECIAL CASE: designated_printer role - showing only Print Queue');
    navigationItems.push({
      id: 'print-queue',
      label: 'Print Queue',
      path: '/idcards/printing-queue',
      icon: 'Print'
    });
    return navigationItems; // Return early, don't show other menus
  }

  // Dashboard - always show for other roles
  navigationItems.push({
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'Dashboard'
  });

  // Permission-based navigation (not role-based)
  const permissionNavMap = {
    // System Administration (Superadmin)
    'manage_tenants': {
      id: 'tenants',
      label: 'Tenants',
      path: '/tenants',
      icon: 'Business'
    },
    'manage_all_tenants': {
      id: 'tenants',
      label: 'Tenants',
      path: '/tenants',
      icon: 'Business'
    },
    'full_system_access': {
      id: 'tenants',
      label: 'Tenants',
      path: '/tenants',
      icon: 'Business'
    },

    // City Level - Citizen Dictionary
    'view_child_subcities_data': {
      id: 'citizen-dictionary',
      label: 'Citizen Dictionary',
      path: '/citizens/citizen-book',
      icon: 'People'
    },

    // Subcity Level - Citizens (All Kebeles)
    'view_child_kebeles_data': {
      id: 'citizens',
      label: 'Citizens',
      path: '/citizens/all-kebeles',
      icon: 'People'
    },

    // Kebele Level - Citizens (Local)
    'view_citizens_list': {
      id: 'citizens',
      label: 'Citizens',
      path: '/citizens',
      icon: 'People'
    },
    'register_citizens': {
      id: 'citizens',
      label: 'Citizens',
      path: '/citizens',
      icon: 'People'
    },

    // Subcity Level - ID Cards (All Kebeles)
    'print_id_cards': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards/all-kebeles',
      icon: 'Badge'
    },

    // Kebele Level - ID Cards (Local)
    'view_id_cards_list': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },
    'generate_id_cards': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },
    'approve_id_cards': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },
    'verify_documents': {
      id: 'idcards',
      label: 'ID Cards',
      path: '/idcards',
      icon: 'Badge'
    },

    // Print Queue
    'print_idcards': {
      id: 'print-queue',
      label: 'Print Queue',
      path: '/idcards/printing-queue',
      icon: 'Print'
    },
    'print_id_cards': {
      id: 'print-queue',
      label: 'Print Queue',
      path: '/idcards/printing-queue',
      icon: 'Print'
    },

    // User Management
    'view_user_management': {
      id: 'user-management',
      label: 'User Management',
      path: '/users/kebele-management',
      icon: 'SupervisorAccount'
    },
    'manage_all_users': {
      id: 'user-management',
      label: 'User Management',
      path: '/users/kebele-management',
      icon: 'SupervisorAccount'
    },
    'create_kebele_users': {
      id: 'kebele-management',
      label: 'Kebele Management',
      path: '/users/kebele-management',
      icon: 'SupervisorAccount'
    },
    'create_subcity_users': {
      id: 'subcity-management',
      label: 'Subcity Management',
      path: '/users/city-management',
      icon: 'SupervisorAccount'
    },

    // Reports
    'view_all_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },
    'view_subcity_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },
    'view_kebele_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },
    'view_city_reports': {
      id: 'reports',
      label: 'Reports',
      path: '/reports',
      icon: 'Assessment'
    },

    // Transfer and Clearance workflows
    'create_transfers': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'approve_transfer_requests': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'view_transfers': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'manage_transfers': {
      id: 'transfers',
      label: 'Transfer',
      path: '/transfers',
      icon: 'Timeline'
    },
    'create_clearances': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },
    'view_clearances': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },
    'manage_clearances': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },
    'approve_clearance_requests': {
      id: 'clearances',
      label: 'Clearance',
      path: '/clearances',
      icon: 'Assignment'
    },

    // Fallback for kebele leaders only - if they have approve_id_cards AND are kebele level, they should have Transfer/Clearance
    // This will be handled in the navigation generation logic to check tenant type
  };

  // Add navigation items based on permissions
  if (user.permissions) {
    console.log('🔍 Processing permissions for navigation...');

    // Special case: Superadmin should NOT have Citizens/ID Cards menus (system admin only)
    const isSuperAdmin = user.role === 'superadmin' || user.is_superuser;
    console.log('🔍 Is superadmin:', isSuperAdmin);

    // Declare userTenantType outside the if block so it's available later
    const userTenantType = user.tenant?.type || user.tenant_type;

    if (!isSuperAdmin) {
      // Special handling for ID Cards - choose the right path based on user tenant type
      const hasIDCardPermissions = user.permissions.some(p =>
        ['print_id_cards', 'approve_id_cards', 'view_id_cards_list', 'generate_id_cards'].includes(p)
      );

      // Also check if user is a clerk (clerks should have ID cards access even without specific permissions)
      const isClerk = user.role === 'clerk';

      console.log('🔍 ID CARDS NAVIGATION DEBUG:');
      console.log('🔍 User permissions:', user.permissions);
      console.log('🔍 Has ID card permissions:', hasIDCardPermissions);
      console.log('🔍 Is clerk:', isClerk);
      console.log('🔍 User tenant type:', userTenantType);
      console.log('🔍 User role:', user.role);
      console.log('🔍 Should show ID cards?', hasIDCardPermissions || isClerk);

      if (hasIDCardPermissions || isClerk) {
        console.log('✅ User has ID card permissions or is clerk, determining correct path...');

        if (userTenantType === 'subcity' && user.permissions.includes('print_id_cards')) {
          // Subcity admins should use all-kebeles path
          navigationItems.push({
            id: 'idcards',
            label: 'ID Cards',
            path: '/idcards/all-kebeles',
            icon: 'Badge'
          });
          console.log('✅ Added subcity ID cards navigation: /idcards/all-kebeles');
        } else if (userTenantType === 'kebele' || user.role === 'kebele_leader' || user.role === 'clerk') {
          // Kebele users (including clerks) should use local path
          navigationItems.push({
            id: 'idcards',
            label: 'ID Cards',
            path: '/idcards',
            icon: 'Badge'
          });
          console.log('✅ Added kebele ID cards navigation: /idcards');
        }
      } else {
        console.log('❌ User has no ID card permissions and is not a clerk');
      }
    } else {
      console.log('🚫 Superadmin: Skipping Citizens/ID Cards menus (system admin only)');
    }

    // Process other permissions (excluding ID card ones to avoid conflicts)
    for (const permission of user.permissions) {
      console.log(`🔍 Checking permission: ${permission}`);

      // Skip ID card permissions as they're handled above
      if (['print_id_cards', 'approve_id_cards', 'view_id_cards_list', 'generate_id_cards'].includes(permission)) {
        console.log(`ℹ️ Skipping ID card permission: ${permission} (handled separately)`);
        continue;
      }

      // Skip citizen and ID card permissions for superadmin (system admin should not manage individual citizens/cards)
      if (isSuperAdmin && [
        'register_citizens', 'view_citizens_list', 'view_citizen_details',
        'verify_documents', 'approve_id_cards', 'generate_id_cards', 'view_id_cards_list'
      ].includes(permission)) {
        console.log(`🚫 Skipping operational permission for superadmin: ${permission}`);
        continue;
      }

      const navMapping = permissionNavMap[permission];
      if (navMapping) {
        // Handle normal single nav item mapping
        console.log(`✅ Found navigation item for ${permission}:`, navMapping);
        // Avoid duplicates
        if (!navigationItems.find(item => item.id === navMapping.id)) {
          navigationItems.push(navMapping);
          console.log(`✅ Added navigation item: ${navMapping.label}`);
        } else {
          console.log(`ℹ️ Skipped duplicate navigation item: ${navMapping.label}`);
        }
      } else {
        console.log(`❌ No navigation item found for permission: ${permission}`);
      }
    }

    // Special case: Add Transfer and Clearance for kebele leaders only
    // userTenantType already declared above
    const isKebeleUser = userTenantType === 'kebele' || user.role === 'kebele_leader';
    const hasApprovalPermission = user.permissions.includes('approve_id_cards');

    if (hasApprovalPermission && isKebeleUser) {
      // Add Transfer if not already present
      if (!navigationItems.find(item => item.id === 'transfers')) {
        navigationItems.push({
          id: 'transfers',
          label: 'Transfer',
          path: '/transfers',
          icon: 'Timeline'
        });
      }

      // Add Clearance if not already present
      if (!navigationItems.find(item => item.id === 'clearances')) {
        navigationItems.push({
          id: 'clearances',
          label: 'Clearance',
          path: '/clearances',
          icon: 'Assignment'
        });
      }
    }
  } else {
    console.log('❌ No permissions found for user');
  }

  console.log(`✅ Generated ${navigationItems.length} navigation items for ${user?.role || 'unknown'}`);
  console.log('🔍 Final navigation items:', navigationItems);
  return navigationItems;
};

export default {
  generateDynamicNavigation,
  ROLE_DEFINITIONS
};
