import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Alert,
  Chip
} from '@mui/material';
import {
  Business as BusinessIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import RBACUserManagement from '../../components/UserManagement/RBACUserManagement';
import { usePermissions } from '../../hooks/usePermissions';
import { hasPermission } from '../../utils/multiTenantRBAC';

const CityUserManagement = () => {
  const { user, dynamicRole } = usePermissions();

  // Check if user has permission to manage subcity users
  const canManageSubcityUsers = hasPermission(user, 'create_subcity_users');

  if (!user) {
    return <div>Loading...</div>;
  }

  if (!canManageSubcityUsers) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <SecurityIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            You don't have permission to manage subcity users.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Required permission: <Chip label="create_subcity_users" size="small" />
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Your current role: <Chip label={dynamicRole} size="small" color="primary" />
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BusinessIcon />
          City User Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage users for subcities under your city administration
        </Typography>
      </Box>

      {/* Permission Info */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>City Admin Permissions:</strong> You can create and manage users for subcities that belong to your city.
          Users created will have access to their respective subcity tenants.
        </Typography>
      </Alert>

      {/* User Management Component */}
      <RBACUserManagement 
        tenantType="subcity"
        currentUser={user}
      />

      {/* Help Section */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Available Roles for Subcity Users
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Chip 
            label="Subcity Admin" 
            color="primary" 
            variant="outlined"
            size="small"
          />
          <Chip 
            label="Subcity Clerk" 
            color="secondary" 
            variant="outlined"
            size="small"
          />
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          • <strong>Subcity Admin:</strong> Can manage kebele users and approve ID cards<br/>
          • <strong>Subcity Clerk:</strong> Can verify documents and assist with administrative tasks
        </Typography>
      </Paper>
    </Box>
  );
};

export default CityUserManagement;
