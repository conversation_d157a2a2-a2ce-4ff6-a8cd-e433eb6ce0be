"""
Management command to set up predefined roles with specific permissions for each tenant level.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from django_tenants.utils import schema_context, get_public_schema_name
from users.models import User
from users.models_groups import TenantGroup, GroupMembership
from tenants.models import Tenant
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Set up predefined roles with specific permissions for each tenant level'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--assign-users',
            action='store_true',
            help='Assign existing users to their appropriate role groups',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Setting up predefined roles...'))
        self.stdout.write('='*60)
        
        try:
            # Step 1: Create required permissions
            self.create_required_permissions(options['dry_run'])
            
            # Step 2: Create predefined role groups
            self.create_predefined_roles(options['dry_run'])
            
            # Step 3: Assign existing users to their role groups
            if options['assign_users']:
                self.assign_users_to_roles(options['dry_run'])
            
            self.stdout.write('')
            self.stdout.write('='*60)
            if options['dry_run']:
                self.stdout.write(self.style.WARNING('🔍 This was a dry run - no changes were made'))
            else:
                self.stdout.write(self.style.SUCCESS('✅ Predefined roles setup completed!'))
            
            self.stdout.write('')
            self.stdout.write('📋 Available roles:')
            self.stdout.write('  • clerk (kebele level only)')
            self.stdout.write('  • kebele_leader (kebele level only)')
            self.stdout.write('  • subcity_admin (subcity level only)')
            self.stdout.write('  • subcity_system_admin (subcity level only)')
            self.stdout.write('  • city_admin (city level only)')
            self.stdout.write('  • city_system_admin (city level only)')
            self.stdout.write('  • print_id_cards (kebele autonomous or subcity centralized)')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error during setup: {e}'))
            import traceback
            traceback.print_exc()

    def create_required_permissions(self, dry_run=False):
        """Create the required permissions if they don't exist."""
        self.stdout.write('🔧 Creating required permissions...')
        
        # Get User content type
        user_content_type = ContentType.objects.get_for_model(User)
        
        required_permissions = [
            # Clerk permissions (kebele level)
            ('register_citizens', 'Can register citizens'),
            ('view_citizens_list', 'Can view citizens list'),
            ('view_citizen_details', 'Can view citizen details'),
            ('generate_id_cards', 'Can generate ID cards'),
            ('view_id_cards_list', 'Can view ID cards list'),
            ('view_kebele_dashboard', 'Can view kebele dashboard'),
            
            # Kebele leader permissions (kebele level)
            ('view_own_kebele_data', 'Can view own kebele data'),
            ('view_kebele_reports', 'Can view kebele reports'),
            ('approve_id_cards', 'Can approve ID cards'),
            ('verify_documents', 'Can verify documents'),
            
            # Subcity admin permissions (subcity level)
            ('view_child_kebeles_data', 'Can view child kebeles data'),
            ('view_subcity_dashboard', 'Can view subcity dashboard'),
            ('view_subcity_reports', 'Can view subcity reports'),
            ('print_id_cards', 'Can print ID cards'),
            ('send_id_cards_to_higher_level', 'Can send ID cards to higher level'),
            ('create_kebele_users', 'Can create kebele users'),
            
            # City admin permissions (city level)
            ('view_child_subcities_data', 'Can view child subcities data'),
            ('view_city_dashboard', 'Can view city dashboard'),
            ('view_city_reports', 'Can view city reports'),
            ('manage_tenants', 'Can manage tenants'),
            ('view_all_reports', 'Can view all reports'),
            ('create_subcity_users', 'Can create subcity users'),
            
            # Navigation permissions
            ('navigate_to_dashboard', 'Can navigate to dashboard'),
            ('navigate_to_citizens', 'Can navigate to citizens'),
            ('navigate_to_id_cards', 'Can navigate to ID cards'),
            ('view_user_management', 'Can view user management'),

            # Transfer and Clearance permissions
            ('transfer_citizens', 'Can transfer citizens'),
            ('create_transfers', 'Can create transfer requests'),
            ('approve_transfer_requests', 'Can approve transfer requests'),
            ('view_transfers', 'Can view transfers'),
            ('create_clearances', 'Can create clearance requests'),
            ('view_clearances', 'Can view clearances'),
        ]
        
        created_count = 0
        for codename, name in required_permissions:
            if dry_run:
                exists = Permission.objects.filter(codename=codename, content_type=user_content_type).exists()
                if not exists:
                    self.stdout.write(f'  Would create permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  Permission already exists: {codename}')
            else:
                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    content_type=user_content_type,
                    defaults={'name': name}
                )
                if created:
                    self.stdout.write(f'  ✅ Created permission: {codename}')
                    created_count += 1
                else:
                    self.stdout.write(f'  ℹ️ Permission already exists: {codename}')
        
        self.stdout.write(f'📊 {"Would create" if dry_run else "Created"} {created_count} permissions')

    def create_predefined_roles(self, dry_run=False):
        """Create predefined role groups with their specific permissions."""
        self.stdout.write('🏗️ Creating predefined role groups...')
        
        # Define roles with their permissions and allowed tenant types
        roles_config = {
            'clerk': {
                'description': 'Clerk role for kebele level tenants',
                'tenant_types': ['kebele'],
                'level': 10,
                'permissions': [
                    'register_citizens', 'view_citizens_list', 'view_citizen_details',
                    'generate_id_cards', 'view_id_cards_list', 'view_kebele_dashboard',
                    'navigate_to_dashboard', 'navigate_to_citizens', 'navigate_to_id_cards'
                ]
            },
            'kebele_leader': {
                'description': 'Kebele leader role for kebele level tenants',
                'tenant_types': ['kebele'],
                'level': 20,
                'permissions': [
                    'view_own_kebele_data', 'view_kebele_dashboard', 'view_kebele_reports',
                    'view_citizens_list', 'view_citizen_details', 'view_id_cards_list',
                    'approve_id_cards', 'verify_documents', 'navigate_to_dashboard',
                    'navigate_to_citizens', 'navigate_to_id_cards', 'view_user_management',
                    # Transfer and Clearance permissions
                    'transfer_citizens', 'create_transfers', 'approve_transfer_requests',
                    'view_transfers', 'create_clearances', 'view_clearances'
                ]
            },
            'subcity_admin': {
                'description': 'Subcity admin role for subcity level tenants',
                'tenant_types': ['subcity'],
                'level': 30,
                'permissions': [
                    'view_child_kebeles_data', 'view_subcity_dashboard', 'view_subcity_reports',
                    'approve_id_cards', 'print_id_cards', 'send_id_cards_to_higher_level',
                    'verify_documents', 'create_kebele_users', 'navigate_to_dashboard',
                    'navigate_to_citizens', 'navigate_to_id_cards', 'view_user_management'
                ]
            },
            'subcity_system_admin': {
                'description': 'Subcity system admin role for managing kebele users',
                'tenant_types': ['subcity'],
                'level': 35,
                'permissions': [
                    'create_kebele_users', 'view_user_management', 'navigate_to_dashboard'
                ]
            },
            'city_admin': {
                'description': 'City admin role for city level tenants',
                'tenant_types': ['city'],
                'level': 40,
                'permissions': [
                    'view_child_subcities_data', 'view_city_dashboard', 'view_city_reports',
                    'manage_tenants', 'view_all_reports', 'create_subcity_users',
                    'navigate_to_dashboard', 'view_user_management'
                ]
            },
            'city_system_admin': {
                'description': 'City system admin role for managing subcity users',
                'tenant_types': ['city'],
                'level': 45,
                'permissions': [
                    'create_subcity_users', 'view_user_management', 'navigate_to_dashboard'
                ]
            },
            'print_id_cards': {
                'description': 'ID card printing role for autonomous kebeles or centralized subcities',
                'tenant_types': ['kebele', 'subcity'],
                'level': 15,
                'permissions': [
                    'print_id_cards', 'view_id_cards_list', 'navigate_to_id_cards'
                ]
            }
        }
        
        created_count = 0
        with schema_context(get_public_schema_name()):
            for role_name, config in roles_config.items():
                if dry_run:
                    exists = Group.objects.filter(name=role_name).exists()
                    if not exists:
                        self.stdout.write(f'  Would create role: {role_name}')
                        created_count += 1
                    else:
                        self.stdout.write(f'  Role already exists: {role_name}')
                else:
                    # Create Django group
                    group, created = Group.objects.get_or_create(name=role_name)
                    
                    if created:
                        self.stdout.write(f'  ✅ Created role group: {role_name}')
                        created_count += 1
                    else:
                        self.stdout.write(f'  ℹ️ Role group already exists: {role_name}')
                    
                    # Add permissions to group
                    permissions_added = 0
                    for perm_codename in config['permissions']:
                        try:
                            permission = Permission.objects.get(codename=perm_codename)
                            if not group.permissions.filter(codename=perm_codename).exists():
                                group.permissions.add(permission)
                                permissions_added += 1
                        except Permission.DoesNotExist:
                            self.stdout.write(f'    ⚠️ Permission not found: {perm_codename}')
                    
                    if permissions_added > 0:
                        self.stdout.write(f'    ➕ Added {permissions_added} permissions to {role_name}')
                    
                    # Create or update TenantGroup
                    tenant_group, tg_created = TenantGroup.objects.get_or_create(
                        group=group,
                        defaults={
                            'name': role_name,
                            'description': config['description'],
                            'group_type': 'administrative' if 'admin' in role_name or 'leader' in role_name else 'operational',
                            'level': config['level'],
                            'allowed_tenant_types': config['tenant_types'],
                            'is_active': True,
                            'tenant': None  # Global role
                        }
                    )
                    
                    if not tg_created:
                        # Update existing TenantGroup
                        tenant_group.allowed_tenant_types = config['tenant_types']
                        tenant_group.level = config['level']
                        tenant_group.save()
                        self.stdout.write(f'    🔄 Updated TenantGroup for {role_name}')
        
        self.stdout.write(f'📊 {"Would create" if dry_run else "Created/Updated"} {created_count} role groups')

    def assign_users_to_roles(self, dry_run=False):
        """Assign existing users to their appropriate role groups based on their current role."""
        self.stdout.write('👥 Assigning existing users to role groups...')
        
        if dry_run:
            self.stdout.write('  Would assign users to their role groups based on current roles')
            return
        
        # Find all users across all schemas
        all_users = []
        
        # Check all tenant schemas
        for tenant in Tenant.objects.all():
            try:
                with schema_context(tenant.schema_name):
                    users = User.objects.filter(is_active=True).exclude(role__isnull=True).exclude(role='')
                    for user in users:
                        all_users.append((user, tenant))
            except Exception as e:
                self.stdout.write(f'  ⚠️ Error checking tenant {tenant.name}: {e}')
        
        self.stdout.write(f'  Found {len(all_users)} users with roles')
        
        assigned_count = 0
        with schema_context(get_public_schema_name()):
            for user, user_tenant in all_users:
                try:
                    # Map user role to group name
                    role_mapping = {
                        'clerk': 'clerk',
                        'kebele_leader': 'kebele_leader',
                        'subcity_admin': 'subcity_admin',
                        'city_admin': 'city_admin'
                    }
                    
                    group_name = role_mapping.get(user.role)
                    if not group_name:
                        self.stdout.write(f'    ⚠️ No role mapping for {user.role} (user: {user.email})')
                        continue
                    
                    # Get the group
                    try:
                        group = Group.objects.get(name=group_name)
                        tenant_group = TenantGroup.objects.get(group=group)
                    except (Group.DoesNotExist, TenantGroup.DoesNotExist):
                        self.stdout.write(f'    ❌ Group {group_name} not found')
                        continue
                    
                    # Check if user's tenant type matches the role's allowed tenant types
                    if user_tenant.type not in tenant_group.allowed_tenant_types:
                        self.stdout.write(f'    ⚠️ User {user.email} has role {user.role} but is in {user_tenant.type} tenant (not allowed)')
                        continue
                    
                    # Add user to group
                    group.user_set.add(user)
                    
                    # Create GroupMembership record
                    membership, created = GroupMembership.objects.get_or_create(
                        user_email=user.email,
                        group=tenant_group,
                        defaults={
                            'user_tenant_id': user_tenant.id,
                            'user_tenant_schema': user_tenant.schema_name,
                            'is_primary': True,
                            'assigned_by_email': 'system',
                            'reason': f'Automatic assignment for role: {user.role}',
                            'is_active': True
                        }
                    )
                    
                    if created:
                        self.stdout.write(f'    ✅ Assigned {user.email} ({user.role}) to {group_name} group')
                        assigned_count += 1
                    else:
                        self.stdout.write(f'    ℹ️ {user.email} already in {group_name} group')
                        
                except Exception as e:
                    self.stdout.write(f'    ❌ Error assigning {user.email}: {e}')
        
        self.stdout.write(f'📊 Assigned {assigned_count} users to role groups')
